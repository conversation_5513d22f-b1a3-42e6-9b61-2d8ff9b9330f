#!/bin/bash

# Common Config
EXPIRE_BACKUP_DELETE="ON"
EXPIRE_DAYS=1
TZ=`date +%Y%m%d%H%M%S`

# MALI
MALI_BACKUP_DIR=$PWD/mali/data
MALI_MYSQL_CONTAINER=gva-mysql
MALI_MYSQL_DB=gva
MALI_MYSQL_PWD=Aa@6447985
# TTRSS
TTRSS_PSQL_CONTAINER=ttrss-postgres
TTRSS_BACKUP_DIR=$PWD/ttrss/data
TTRSS_PSQL_DB=ttrss

/usr/bin/docker exec ${MALI_MYSQL_CONTAINER} mysqldump -uroot -p${MALI_MYSQL_PWD} ${MALI_MYSQL_DB} | gzip > ${MALI_BACKUP_DIR}/${MALI_MYSQL_DB}_${TZ}.sql.gz

/usr/bin/docker exec  ${TTRSS_PSQL_CONTAINER} pg_dump -U postgres -d ${TTRSS_PSQL_DB} | gzip > ${TTRSS_BACKUP_DIR}/${TTRSS_PSQL_DB}_${TZ}.sql.gz

# Exception recognition, or else unsuccessful backups for a long time will delete the previous backups
if [ $? -ne 0 ]; then
    echo "failed to backup :<"
    exit 1
else
    echo "succeed to backup :>"
fi

# Delete Expires Data
if [ ${EXPIRE_BACKUP_DELETE} == "ON" ];then
    `find ${MALI_BACKUP_DIR}/ -type f -mtime +${EXPIRE_DAYS} | xargs rm -rf`
    `find ${TTRSS_BACKUP_DIR}/ -type f -mtime +${EXPIRE_DAYS} | xargs rm -rf`
    echo ${TZ}"Expired backup data delete complete!"
fi

# Git Comit
git add .
git commit -m "feat: ttrss及mali数据库的sql文件"
git push -f


# [Docker 中 MySQL 数据的导入导出-阿里云开发者社区](https://developer.aliyun.com/article/611474)

# 注意参数-i,而非-it
# docker exec -i gva-mysql gva < gva.sql

# [docker postgres 导出导入数据 - 静静别跑 - 博客园](https://www.cnblogs.com/zhzhlong/p/11466464.html)

# 导入pgsql数据库
#docker cp ./ttrss.sql ttrss-postgres:/
#docker exec ttrss-postgres sh -c 'exec psql -U postgres -d ttrss < ./ttrss.sql'

# [pgsql数据库自动备份_10月月更_衝鋒壹号_InfoQ写作社区](https://xie.infoq.cn/article/312cee6ac165c8c94dc46457e)
