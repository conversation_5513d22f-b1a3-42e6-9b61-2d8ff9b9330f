[mysql]
172.16.56.61 ansible_ssh_private_key_file=ssh_keys/
172.16.56.41 ansible_ssh_private_key_file=ssh_keys/xxx
#172.16.56.82
172.16.56.76
172.16.56.96
172.16.63.27
172.16.63.28
172.16.56.104 ansible_ssh_private_key_file=ssh_keys/xxx
172.16.56.119
172.16.55.44 

[postgre]
172.16.55.46
172.16.56.145 ansible_ssh_private_key_file=/etc/ansible/ssh_keys/ops_prod
172.16.56.146 ansible_ssh_private_key_file=/etc/ansible/ssh_keys/ops_prod

[big-mg]
#172.16.56.59
#172.16.56.48
#172.16.56.56
#172.16.56.61
172.16.56.104
172.16.56.94
172.16.56.106

[big-opt]
#172.16.56.62
#172.16.56.41

[big-dn]
#172.16.56.55
#172.16.56.38
#172.16.56.57
#172.16.56.65
#172.16.56.51
#172.16.56.58
#172.16.56.63
#172.16.56.42
#172.16.56.69
#172.16.56.68
172.16.56.108
172.16.56.116  ansible_ssh_private_key_file=ssh_keys/xxx
172.16.56.118  ansible_ssh_private_key_file=ssh_keys/xxx
172.16.56.105
172.16.56.101
172.16.56.92
172.16.56.103
172.16.56.52

[keepalive]
172.16.63.17
172.16.63.18


[bigdata:children]
big-mg
big-opt
big-dn

[sms-prod]
172.16.56.72 


[cd-vm:children]
ops-prod-pre-vm
dev-test-vm
bigdata
mysql
pre-bigdata
bj-vm
postgre


[ops-prod-pre-vm:children]
prod-vm
pre-vm
ops-vm

[prod-vm]
172.16.63.[4:9]
172.16.63.[35:38]
172.16.63.28
172.16.63.27
172.16.63.[20:25]
172.16.63.[11:18]
172.16.63.[1:3]
172.16.56.[72:74]
172.16.63.40
172.16.63.41

[pre-vm]
172.16.56.81
172.16.56.125
172.16.56.123
172.16.56.129
172.16.56.109
172.16.56.95
172.16.56.119
172.16.56.117
172.16.56.132
172.16.56.75
172.16.63.[30:33]
172.16.56.44
172.16.56.91

[ops-vm]
172.16.56.[140:142]
172.16.56.97
172.16.56.143 ansible_ssh_private_key_file=ssh_keys/xxx
172.16.56.66
172.16.56.96
172.16.56.61
172.16.56.60
172.16.56.40
172.16.56.[110:114]
172.16.56.47
172.16.56.36
172.16.56.39 
172.16.56.70
172.16.56.134
172.16.56.130
172.16.56.135
172.16.56.144 ansible_ssh_private_key_file=/etc/ansible/ssh_keys/ops_prod

[bj-vm]
172.21.57.192
172.21.57.184
172.21.57.177
172.21.57.163
172.21.56.15 ansible_ssh_pass='Ops@2018' ansible_ssh_user='root'
172.21.56.18 ansible_ssh_pass='Ops@2018' ansible_ssh_user='root'
172.21.57.10 ansible_ssh_private_key_file=ssh_keys/xxx

[dev-test-vm:children]
dev-vm
test-vm

[dev-vm]
172.16.56.124
172.16.56.126 ansible_ssh_private_key_file=ssh_keys/xxx 
172.16.56.41
172.16.56.53
172.16.56.64
172.16.56.67
172.16.56.71
172.16.56.79
172.16.56.80
172.16.56.89
172.16.56.98
172.16.56.127
172.16.55.38 ansible_ssh_pass='Ops@2018' ansible_ssh_user='root'
172.16.55.51 ansible_ssh_pass='Ops@2018' ansible_ssh_user='root'
172.16.55.47 ansible_ssh_pass='Ops@2018' ansible_ssh_user='root'
172.16.52.24 ansible_ssh_private_key_file=/etc/ansible/ssh_keys/ops_prod
172.16.52.25 ansible_ssh_private_key_file=/etc/ansible/ssh_keys/ops_prod

[test-vm]
172.16.56.34
172.16.56.101
172.16.56.[103:108]
172.16.56.116
172.16.56.118
172.16.56.[120:121]
172.16.56.[33:34]
172.16.56.[37:38]
172.16.56.42
172.16.56.46
172.16.56.[48:49]
172.16.56.[51:52]
172.16.56.[54:59]
172.16.56.35
172.16.56.122
172.16.56.[62:63]
172.16.56.65
172.16.56.[68:69]
172.16.56.[75:78]
#172.16.56.82
172.16.56.[83:88]
172.16.56.90
172.16.56.[92:94]
172.16.56.99
172.16.57.[10:17]
172.16.57.[50:52]
172.16.57.54
172.16.57.100
172.16.55.53 ansible_ssh_private_key_file=/etc/ansible/ssh_keys/ops_prod
172.16.55.51 ansible_ssh_private_key_file=/etc/ansible/ssh_keys/ops_prod
172.16.55.58 ansible_ssh_private_key_file=/etc/ansible/ssh_keys/ops_prod
172.16.55.71 ansible_ssh_pass='ops@2018' ansible_ssh_user='root'
172.16.55.72 ansible_ssh_pass='ops@2018' ansible_ssh_user='root'
172.16.55.73 ansible_ssh_pass='ops@2018' ansible_ssh_user='root'
172.16.55.74 ansible_ssh_pass='ops@2018' ansible_ssh_user='root'
172.16.55.75 ansible_ssh_pass='ops@2018' ansible_ssh_user='root'
172.16.55.76 ansible_ssh_pass='ops@2018' ansible_ssh_user='root'
172.16.55.77 ansible_ssh_pass='ops@2018' ansible_ssh_user='root'
172.16.55.68 ansible_ssh_pass='ops@2018' ansible_ssh_user='root'
172.16.55.67 ansible_ssh_pass='ops@2018' ansible_ssh_user='root'
172.16.55.69 ansible_ssh_pass='ops@2018' ansible_ssh_user='root'
172.16.55.91 ansible_ssh_pass='ops@2018' ansible_ssh_user='root'
172.16.55.86 ansible_ssh_pass='ops@2018' ansible_ssh_user='root'
172.16.55.67 ansible_ssh_pass='ops@2018' ansible_ssh_user='root'
172.20.57.173 ansible_ssh_pass='ops' ansible_ssh_user='root'
172.20.57.167 ansible_ssh_pass='ops' ansible_ssh_user='root'
172.21.57.199 ansible_ssh_pass='ops' ansible_ssh_user='root'
172.21.57.200 ansible_ssh_pass='ops' ansible_ssh_user='root'
172.21.57.201 ansible_ssh_pass='ops' ansible_ssh_user='root'
172.21.57.202 ansible_ssh_pass='ops' ansible_ssh_user='root'
172.16.56.131 
192.168.16.114 ansible_ssh_pass='ops@' ansible_ssh_user='root'

[openstack]
#172.16.56.103
#172.16.56.94
#172.20.52.[19:42]
#172.20.200.[2:18]
172.16.52.[16:23]

#[openstack:vars]
#ansible_ssh_pass='ops'
#ansible_ssh_user='root'

[bj-mdm-test]
172.21.56.9 ansible_ssh_private_key_file=ssh_keys/xxx
172.21.56.10 ansible_ssh_private_key_file=ssh_keys/xxx

[bj-test]
172.21.56.5
172.21.56.14
172.21.56.11
172.21.56.17
172.21.57.24


[pre-bigdata]
172.16.56.123
172.16.56.129
172.16.56.109
172.16.56.95
172.16.56.119
172.16.56.125
172.16.56.81

[physical]
172.16.52.[2:254]
192.168.16.40 ansible_ssh_pass='ops@2018' ansible_ssh_user='root'
192.168.16.41 
[physical:children]
cd-openstack

[cd-openstack]
172.16.52.[2:15]


[pgsql]
172.16.56.78

[qianzhiji]
192.168.16.45
192.168.16.46
172.22.55.2
172.22.55.3
172.22.55.4
172.22.55.5
172.22.55.6
172.22.55.7
[qianzhiji:vars]
ansible_ssh_user=root 
ansible_ssh_pass=centos

[dv1-de-db2wh]
172.20.57.26
172.20.57.146
172.20.57.17
172.20.57.143
172.20.57.24
172.20.57.112
172.20.57.44
172.20.57.31
172.20.57.12
172.20.57.4
172.20.57.9
172.20.57.5
172.21.56.11 ansible_ssh_private_key_file=ssh_keys/xxx

172.16.55.44 ansible_ssh_pass='centos' ansible_ssh_user='root'

[openstack-ceph]
172.16.56.82 ansible_ssh_private_key_file=ssh_keys/xxx

[zookeepers]
172.16.56.33
172.16.56.120
172.16.56.75

[redis-nodes]
172.16.56.33
172.16.56.75
172.16.56.120

[redis-mgt]
172.16.56.75

[mq-cluster]
172.16.56.33
172.16.56.75
172.16.56.120
