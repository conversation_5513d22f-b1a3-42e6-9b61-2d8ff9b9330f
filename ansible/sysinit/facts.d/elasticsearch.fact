#!/usr/bin/python

import json
import subprocess
import os,sys




def main():
    data = {}
    service_name = "elasticsearch"
    try:
	    pid = subprocess.Popen("""ps -aux | grep %s.bootstrap |grep -vE "color|kafka_exporter|python|aux|grep"|awk '{print $2}'"""%service_name,
	                         shell=True,
	                         stdout=subprocess.PIPE,
	                         stderr=subprocess.PIPE)
            pid = pid.stdout.read().replace('\n','')
            service = subprocess.Popen("systemctl status %s | grep Active | awk '{print $2}'"%service_name,
                                 shell=True,
                                 stdout=subprocess.PIPE,
                                 stderr=subprocess.PIPE)
            service = service.stdout.read().replace('\n','')
            if len(service)!= 0 :
              service_pid = subprocess.Popen("systemctl status %s | grep PID | awk '{print $3}'"% service_name,
                                 shell=True,
                                 stdout=subprocess.PIPE,
                                 stderr=subprocess.PIPE)
              service_pid=service_pid.stdout.read().replace('\n','')
              #port =  subprocess.Popen("netstat -nlp | grep %s |awk '{print $4}' | awk -F ':::' ' {print $2}'"%pid,
              port =  subprocess.Popen("netstat -nlp | grep %s |awk '{split($4,a,\":\");if(a[2]!=\"\"){print a[2];}else{print a[4];}}'"%pid,
                                 shell=True,
                                 stdout=subprocess.PIPE,
                                 stderr=subprocess.PIPE)
              
              port = port.stdout.read().replace('\n',',')
              port = port[:-1]
              if service == 'active':
                if service_pid == pid:
                     data["port"]= port
            else:
              docker_pid = subprocess.Popen("docker top $(docker ps | grep %s |awk '{print $1}') |awk 'NR==2 {print $2}'"% service_name,
                                 shell=True,
                                 stdout=subprocess.PIPE,
                                 stderr=subprocess.PIPE)
              docker_pid = docker_pid.stdout.read().replace('\n','')
              if pid == docker_pid:
                docker_port = subprocess.Popen("docker port $(docker ps | grep %s |awk '{print $1}') "% service_name,
                                   shell=True,
                                   stdout=subprocess.PIPE,
                                   stderr=subprocess.PIPE)
                docker_port = docker_port.stdout.read().replace('\n',',')
                docker_port = docker_port[:-1]
                docker_port = docker_port.split(',')
                ports1=''
                for i in docker_port:
                    port = i.split(":")[1] 
                    ports1 = port +',' +ports1
                data['port']= ports1[:-1]
          
	    print json.dumps(data)
    
    except Exception as e:
        print json.dumps(data)
    	
  
if __name__=='__main__':
    main()
