#!/usr/bin/python

import json
import subprocess
import os,sys

def main():
    data = {}
    service_name = "kafka"
    try:
	    pid = subprocess.Popen("""ps -aux | grep %s | grep -vE "color|kafka_exporter|python|aux|grep"|awk '{print $2}'"""%service_name,
	                         shell=True,
	                         stdout=subprocess.PIPE,
	                         stderr=subprocess.PIPE)
            pid = pid.stdout.read().replace('\n','')
            service = subprocess.Popen("systemctl status %s | grep Active | awk '{print $2}'"%service_name,
                                 shell=True,
                                 stdout=subprocess.PIPE,
                                 stderr=subprocess.PIPE)
            service = service.stdout.read().replace('\n','')
            service_pid = subprocess.Popen("systemctl status %s | grep PID | awk '{print $3}'"% service_name,
                                 shell=True,
                                 stdout=subprocess.PIPE,
                                 stderr=subprocess.PIPE)
            service_pid=service_pid.stdout.read().replace('\n','')
           # port =  subprocess.Popen("netstat -nlp | grep %s |awk '{print $4}' | awk -F ':' 'NR==2 {print $2}'"%pid,
            port =  subprocess.Popen("netstat -nlp | grep %s |awk '{split($4,a,\":\");if(a[2]!=\"\"){print a[2];}else{print a[4];}}'"%pid,
                                 shell=True,
                                 stdout=subprocess.PIPE,
                                 stderr=subprocess.PIPE)
            port = port.stdout.read().replace('\n',',')
            port = port[:-1]
            
            if service == 'active':
              if service_pid == pid: 
                   data["port"]= port
	        
	    
	    	
	    print json.dumps(data)
    
    except Exception as e:
        print json.dumps(data)
    	
  
if __name__=='__main__':
    main()
