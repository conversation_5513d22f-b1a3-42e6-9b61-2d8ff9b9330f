#!/usr/bin/python

import json
import subprocess

def main():
    data = {}
    
    try:
	    rabbit = subprocess.Popen("netstat -antlp|grep 5672|grep beam.smp|awk 'NR==1 {split($4,a,\":\");if(a[2]!=\"\"){print a[2];}else{print a[4];}}'",
	                         shell=True,
	                         stdout=subprocess.PIPE,
	                         stderr=subprocess.PIPE)
	    output = rabbit.stdout.read()
	    if output:
	        data["port"] = output.strip()
	        
	    
	    list_vhosts = subprocess.Popen("rabbitmqctl list_vhosts|awk 'NR>1 {if($0!=\"...done.\"){print $0}}'",
	                         shell=True,
	                         stdout=subprocess.PIPE,
	                         stderr=subprocess.PIPE)
	    vhosts_output = list_vhosts.stdout.read()
	    if vhosts_output:
	        vhosts = vhosts_output.split("\n")
	        vhosts.remove('')
	    	data["databases"] = vhosts
	    	
	    print json.dumps(data)
    
    except Exception as e:
        print json.dumps(data)
    	
  
if __name__=='__main__':
    main()
