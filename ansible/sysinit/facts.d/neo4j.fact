#!/usr/bin/python

import json
import subprocess

def main():
    data = {}
    service_name = "neo4j"
    res_port = []
    try:
	    pid = subprocess.Popen("""ps -aux | grep %s | grep -vE "color|kafka_exporter|python|aux|grep|dhclient"|awk '{print $2}'"""%service_name,
	                         shell=True,
	                         stdout=subprocess.PIPE,
	                         stderr=subprocess.PIPE)
            pid = pid.stdout.read().replace('\n','')
            service = subprocess.Popen("systemctl status %s | grep Active | awk '{print $2}'"%service_name,
                                 shell=True,
                                 stdout=subprocess.PIPE,
                                 stderr=subprocess.PIPE)
            service = service.stdout.read().replace('\n','')
            service_pid = subprocess.Popen("systemctl status %s | grep PID | awk '{print $3}'"% service_name,
                                 shell=True,
                                 stdout=subprocess.PIPE,
                                 stderr=subprocess.PIPE)
            service_pid=service_pid.stdout.read().replace('\n','')
            #port =  subprocess.Popen("netstat -nlp | grep %s |awk '{print $4}' | awk -F ':' '{print $2}'"%pid,
            port =  subprocess.Popen("netstat -nlp | grep %s |awk '{split($4,a,\":\");if(a[2]!=\"\"){print a[2];}else{print a[4];}}'"%pid,
                                 shell=True,
                                 stdout=subprocess.PIPE,
                                 stderr=subprocess.PIPE)
            port = port.stdout.read().replace('\n',',')
            result_port = port[:-1]
            port = result_port.split(',')
            
            config_path = subprocess.Popen("systemctl status %s -l | grep config-dir |awk '{print $NF}'"% service_name,
                                 shell=True,
                                 stdout=subprocess.PIPE,
                                 stderr=subprocess.PIPE)
            config_path = config_path.stdout.read().replace('\n','')
            config_path = config_path.split('=')
            config_sp = config_path[1]
            config_fp = config_sp +"/neo4j.conf"
            config_context = subprocess.Popen("cat %s | grep listen_address | grep -E 'http|https|bolt'|awk -F ':' '{print $2}'"%config_fp,
                                 shell=True,
                                 stdout=subprocess.PIPE,
                                 stderr=subprocess.PIPE)
            config_context = config_context.stdout.read().replace('\n',',')
            num = 0
            if service == 'active':
              if service_pid == pid: 
                for index,j in enumerate(port):
                  if j in config_context:
                     num = num +1
                if num == len(port): 
                   data["port"]= result_port
                 
	        
            print json.dumps(data)
	    
    
    except Exception as e:
        print json.dumps(data)
    	
  
if __name__=='__main__':
    main()
