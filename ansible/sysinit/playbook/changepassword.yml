---
- hosts: prod-vm
  gather_facts: true
  remote_user: root
  tasks:
    - name: transfer file to server
    #copy: src=/etc/ansible/playbook/roles/initial/files/pswd.sh dest=/tmp/pswd.sh mode=755
      copy: src=/etc/ansible/playbook/roles/initial/files/pswd.sh dest=/tmp/pswd.sh mode=755
    - name: change the password
      become: true
      become_method: su
      shell: /bin/bash -x /tmp/pswd.sh
