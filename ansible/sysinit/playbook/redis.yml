---
- hosts: redis-nodes
  become: true
  roles:
    - redis/core
    - { role: redis/node, redis_port: 7000 }
    - { role: redis/node, redis_port: 7001 }

- hosts: redis-mgt
  become: true
  roles:
    - role: redis/cluster
      redis_cluster_replicas: 1
      redis_node_list: "{{ groups['redis-nodes'] | map('extract', hostvars, ['ansible_eth0', 'ipv4', 'address']) | arraypermute( [':'] ) | arraypermute( [7000,7001]
        ) | list }}"
