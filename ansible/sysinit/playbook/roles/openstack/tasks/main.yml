---
- name: Create Dir
  file: path={{ item }} state=directory
  with_items:
    - /opt/tools
    - /opt/scripts
    - /opt/database
  tags:
    - sysinit

- name: Create Group ops
  group:
    name: ops
    state: present
  when: "'ldap' not in host_type"
  tags:
    - sysinit
    - create_user

- name: Create User ops
  user:
    name: ops
    shell: /bin/bash
    group: ops
  when: "'ldap' not in host_type"
  tags:
    - sysinit
    - create-user

- name: Create Ops User Dir
  file: path={{ item }} state=directory mode='0755' owner=ops group=ops
  with_items:
    - /opt/ops/app
    - /opt/ops/files
  tags:
    - sysinit

- name: Create Log Dir
  file:
    path: /var/log/app
    state: directory
    mode: "0755"
  tags:
    - sysinit

- name: back yum repo file
  shell: mv /etc/yum.repos.d /etc/yum.repos.d-$(date  +%F-%H-%M-%S)
  tags:
    - create_repo
    - sysinit

- name: Create yum repo Dir
  file:
    path: /etc/yum.repos.d
    state: directory
    mode: "0755"
  tags:
    - sysinit
    - create_repo
    - create_repo_dir

- name: Add repo ops
  yum_repository:
    name: ops
    description: ops YUM repo
    file: ops
    baseurl: http://*************/7/$basearch/
    enabled: true
    gpgcheck: false
  tags:
    - sysinit
    - repo_ops
    - create_repo

- name: Add RedHat System Parameter File
  copy: src={{ item.src }} dest={{ item.dest }} mode='0755' backup=yes
  with_items:
    - { src: redhat-base.repo, dest: /etc/yum.repos.d/redhat-base.repo }
    - { src: epel.repo, dest: /etc/yum.repos.d/epel.repo }
    - { src: epel-testing.repo, dest: /etc/yum.repos.d/epel-testing.repo }
    - { src: limits.conf, dest: /etc/security/limits.conf }
    - { src: sysctl.conf, dest: /etc/sysctl.conf }
    - { src: docker-ce.repo, dest: /etc/yum.repos.d/docker-ce.repo }
    - { src: gpfs.repo, dest: /etc/yum.repos.d/gpfs.repo }
  when: ansible_distribution == 'RedHat'
  tags:
    - base_repo
    - config
    - create_repo

- name: Add CentOS System Parameter File
  copy: src={{ item.src }} dest={{ item.dest }} mode='0755' backup=yes
  with_items:
    - { src: CentOS-Base.repo, dest: /etc/yum.repos.d/CentOS-Base.repo }
    - { src: epel.repo, dest: /etc/yum.repos.d/epel.repo }
    - { src: epel-testing.repo, dest: /etc/yum.repos.d/epel-testing.repo }
    - { src: limits.conf, dest: /etc/security/limits.conf }
    - { src: sysctl.conf, dest: /etc/sysctl.conf }
    - { src: docker-ce.repo, dest: /etc/yum.repos.d/docker-ce.repo }
    - { src: gpfs.repo, dest: /etc/yum.repos.d/gpfs.repo }
  when: ansible_distribution == "CentOS"
  tags:
    - base_repo
    - config
    - create_repo

- name: Change system.conf DefaultLimitCORE
  lineinfile:
    dest: /etc/systemd/system.conf
    regexp: DefaultLimitCORE
    backrefs: false
    line: DefaultLimitCORE=infinity
  tags:
    - sysinit

- name: Change system.conf DefaultLimitNOFILE
  lineinfile:
    dest: /etc/systemd/system.conf
    regexp: DefaultLimitNOFILE
    backrefs: false
    line: DefaultLimitNOFILE=100000
  tags:
    - sysinit

- name: Change system.conf
  lineinfile:
    dest: /etc/systemd/system.conf
    regexp: DefaultLimitNPROC
    backrefs: false
    line: DefaultLimitNPROC=100000
  tags:
    - sysinit
    - change_system_parameter

- name: Selinux Disable
  lineinfile:
    dest: /etc/selinux/config
    regexp: ^SELINUX=
    line: SELINUX=disabled
  tags:
    - sysinit
    - selinux_dsiable

- name: Selinux stop
  selinux: state=disabled
  tags:
    - sysinit
    - selinux_stop

- name: Set Timezone
  timezone: name=Asia/Shanghai
  tags:
    - sysinit
    - set_timezone

- name: ssh connect timeout setup
  lineinfile:
    dest: /etc/ssh/sshd_config
    regexp: ^#?{{ item.option }}.+$
    line: "{{ item.option }} {{ item.value }}"
    state: "{{ item.state | default('present') }}"
  with_items: "{{ ssh_config }}"
  notify: restart sshd
  tags:
    - sysinit
    - ssh_timeout

#- name: ssh connect timeout setup
#  lineinfile:
#    dest: "/etc/ssh/sshd_config"
#    regexp: "^#?{{ item.option }}.+$"
#    line: "{{ item.option }} {{ item.value }}"
#    state: "{{ item.state | default('present') }}"
#  with_items: "{{ ssh_config }}"
#  notify: restart sshd
#  tags:
#    - sysinit
#    - ssh_timeout

- name: 取消ssh首次连接确认
  lineinfile:
    dest: /etc/ssh/ssh_config
    regexp: ^#?StrictHostKeyChecking.+$
    line: StrictHostKeyChecking no
    state: present
  tags:
    - sysinit
    - ssh_keys

- name: history record env set
  lineinfile:
    dest: /etc/bashrc
    regexp: ^#?{{ item.option }}.+$
    line: "{{ item.option }}={{ item.value }}"
    state: "{{ item.state | default('present') }}"
  with_items: "{{ etc_bashrc }}"
  tags:
    - sysinit
    - history_record

- name: command record
  lineinfile:
    dest: /etc/rsyslog.conf
    regexp: ^#?local5.+$
    line: local5.*  /var/log/history.log
    state: present
  notify: restart rsyslog
  tags:
    - sysinit
    - history_record

- name: Copy id_rsa
  copy:
    src: id_rsa_openstack
    dest: /root/.ssh/id_rsa
    mode: "0600"
  tags:
    - sysinit
    - ssh_keys

- name: Set authorized key from file
  authorized_key:
    user: root
    state: present
    key: "{{ lookup('file', 'authorized_keys') }}"
  tags:
    - sysinit
    - ssh_keys

#- name: Add /etc/hosts
#  lineinfile:
#    dest: /etc/hosts
#    regexp: "^#?{{ item }}.+$"
#    line: "{{ item }} {{ hostvars[item]['ansible_hostname'] }}"
#  with_inventory_hostnames:
#    - openstack
##  with_items:
##    - "{{ groups['openstack'] }}"
#  tags:
#    - sysinit
#    - etc_hosts
#
#- name: replace /etc/hosts ip
#  replace:
#    dest: "/etc/hosts"
#    regexp: "52"
#    replace: "200"
#  tags:
#    - sysinit
#    - etc_hosts

#- name: install kernel-devel,kernel-handers
#  yum:
#    name: 
#      - kernel-devel-3.10.0-693.17.1.el7.x86_64
#      - kernel-headers-3.10.0-693.17.1.el7.x86_64
#      - kernel-3.10.0-693.17.1.el7.x86_64
#    state: present
#    update_cache: yes
#  tags:
#    - sysinit
#    - yum_install

- name: install gpfs rpm
  yum:
    name:
      - binutils
      - nfs-utils
      - numactl
      - python
      - boost-regex
      - pyparsing
      - ksh
      - m4
      - gpfs.adv
      - gpfs.base
      - gpfs.compression
      - gpfs.crypto
      - gpfs.docs
      - gpfs.ext
      - gpfs.gpl
      - gpfs.gskit
      - gpfs.java
      - gpfs.license.dm
      - gpfs.msg.en_US
      - gpfs.nfs-ganesha
      - gpfs.nfs-ganesha-gpfs
      - gpfs.nfs-ganesha-utils
      - gpfs.pm-ganesha
      - gpfs.gss.pmsensors
      - gpfs.pm-ganesha
    #      - gpfs.gplbin-3.10.0-693.17.1.el7.x86_64-5.0.2-2.x86_64
    state: present
    update_cache: true
  tags:
    - sysinit
    - gpfs_install

#- name: copy file hosts
#  copy: src={{ item.src }} dest={{ item.dest }} mode='0644' backup=yes
#  with_items:
#    - {src: "hosts", dest: "/etc/hosts"}
#  tags:
#    - copy_hosts
