---
- name: Ensure PostgreSQL databases are present.
  postgresql_db:
    name: "{{ item.name }}"
    lc_collate: "{{ item.lc_collate | default('en_US.UTF-8') }}"
    lc_ctype: "{{ item.lc_ctype | default('en_US.UTF-8') }}"
    encoding: "{{ item.encoding | default('UTF-8') }}"
    template: "{{ item.template | default('template0') }}"
    login_host: "{{ item.login_host | default('localhost') }}"
    login_password: "{{ item.login_password | default(omit) }}"
    login_user: "{{ item.login_user | default(postgresql_user) }}"
    login_unix_socket: "{{ item.login_unix_socket | default(postgresql_unix_socket_directories[0]) }}"
    port: "{{ item.port | default(omit) }}"
    owner: "{{ item.owner | default(postgresql_user) }}"
    state: "{{ item.state | default('present') }}"
  with_items: "{{ postgresql_databases }}"
  become: true
  become_user: "{{ postgresql_user }}"
  # See: https://github.com/ansible/ansible/issues/16048#issuecomment-229012509
  vars:
    ansible_ssh_pipelining: true
