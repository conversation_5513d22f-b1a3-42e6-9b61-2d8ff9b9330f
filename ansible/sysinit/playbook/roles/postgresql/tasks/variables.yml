---
# Variable configuration.
- name: Include OS-specific variables (Debian).
  include_vars: "{{ ansible_distribution }}-{{ ansible_distribution_version.split('.')[0] }}.yml"
  when: ansible_os_family == 'Debian'

- name: Include OS-specific variables (RedHat).
  include_vars: "{{ ansible_os_family }}-{{ ansible_distribution_version.split('.')[0] }}.yml"
  when: ansible_os_family == 'RedHat'

- name: Define postgresql_packages.
  set_fact:
    postgresql_packages: "{{ __postgresql_packages | list }}"
  when: postgresql_packages is not defined

- name: Define postgresql_version.
  set_fact:
    postgresql_version: "{{ __postgresql_version }}"
  when: postgresql_version is not defined

- name: Define postgresql_daemon.
  set_fact:
    postgresql_daemon: "{{ __postgresql_daemon }}"
  when: postgresql_daemon is not defined

- name: Define postgresql_data_dir.
  set_fact:
    postgresql_data_dir: "{{ __postgresql_data_dir }}"
  when: postgresql_data_dir is not defined

- name: Define postgresql_bin_path.
  set_fact:
    postgresql_bin_path: "{{ __postgresql_bin_path }}"
  when: postgresql_bin_path is not defined

- name: Define postgresql_config_path.
  set_fact:
    postgresql_config_path: "{{ __postgresql_config_path }}"
  when: postgresql_config_path is not defined
