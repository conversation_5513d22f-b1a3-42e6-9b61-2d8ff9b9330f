---
dependency:
  name: galaxy
driver:
  name: docker
lint:
  name: yamllint
  options:
    config-file: molecule/default/yaml-lint.yml
platforms:
  - name: instance
    image: geerlingguy/docker-${MOLECULE_DISTRO:-centos7}-ansible:latest
    command: ${MOLECULE_DOCKER_COMMAND:-""}
    volumes:
      - /sys/fs/cgroup:/sys/fs/cgroup:ro
    privileged: true
    pre_build_image: true
provisioner:
  name: ansible
  lint:
    name: ansible-lint
  playbooks:
    converge: ${MOLECULE_PLAYBOOK:-playbook.yml}
scenario:
  name: default
verifier:
  name: testinfra
  lint:
    name: flake8
