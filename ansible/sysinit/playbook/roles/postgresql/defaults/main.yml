---
# RHEL/CentOS only. Set a repository to use for PostgreSQL installation.
postgresql_enablerepo: ""

# Set postgresql state when configuration changes are made. Recommended values:
# `restarted` or `reloaded`
postgresql_restarted_state: restarted

postgresql_python_library: python-psycopg2
postgresql_user: postgres
postgresql_group: postgres

postgresql_unix_socket_directories:
  - /var/run/postgresql

postgresql_service_state: started
postgresql_service_enabled: true

# Global configuration options that will be set in postgresql.conf.
postgresql_global_config_options:
  - option: unix_socket_directories
    value: '{{ postgresql_unix_socket_directories | join(",") }}'
  - option: listen_addresses
    value: "*"

# Host based authentication (hba) entries to be added to the pg_hba.conf. This
# variable's defaults reflect the defaults that come with a fresh installation.
postgresql_hba_entries:
  - { type: local, database: all, user: postgres, auth_method: trust }
  - { type: local, database: all, user: all, auth_method: peer }
  - { type: host, database: all, user: all, address: 127.0.0.1/32, auth_method: md5 }
  - { type: host, database: all, user: all, address: **********/16, auth_method: md5 }
  - { type: host, database: all, user: all, address: "::1/128", auth_method: md5 }

# Debian only. Used to generate the locales used by PostgreSQL databases.
postgresql_locales:
  - en_US.UTF-8

# Databases to ensure exist.
postgresql_databases: []
# - name: exampledb # required; the rest are optional
#   lc_collate: # defaults to 'en_US.UTF-8'
#   lc_ctype: # defaults to 'en_US.UTF-8'
#   encoding: # defaults to 'UTF-8'
#   template: # defaults to 'template0'
#   login_host: # defaults to 'localhost'
#   login_password: # defaults to not set
#   login_user: # defaults to '{{ postgresql_user }}'
#   login_unix_socket: # defaults to 1st of postgresql_unix_socket_directories
#   port: # defaults to not set
#   owner: # defaults to postgresql_user
#   state: # defaults to 'present'

# Users to ensure exist.
postgresql_users: []
# - name: jdoe #required; the rest are optional
#   password: # defaults to not set
#   encrypted: # defaults to not set
#   priv: # defaults to not set
#   role_attr_flags: # defaults to not set
#   db: # defaults to not set
#   login_host: # defaults to 'localhost'
#   login_password: # defaults to not set
#   login_user: # defaults to '{{ postgresql_user }}'
#   login_unix_socket: # defaults to 1st of postgresql_unix_socket_directories
#   port: # defaults to not set
#   state: # defaults to 'present'
