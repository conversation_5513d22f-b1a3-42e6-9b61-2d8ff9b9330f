---
# Variable configuration.
- include_tasks: variables.yml

# Log segmentation.
- include_tasks: log.yml

# Setup/install tasks.
- include_tasks: setup-RedHat.yml
  when: ansible_os_family == 'RedHat'

- include_tasks: setup-Debian.yml
  when: ansible_os_family == 'Debian'

- include_tasks: setup-Archlinux.yml
  when: ansible_os_family == 'Archlinux'

- name: Check if MySQL packages were installed.
  set_fact:
    mysql_install_packages: "{{ (rh_mysql_install_packages is defined and rh_mysql_install_packages.changed) or (deb_mysql_install_packages is defined and deb_mysql_install_packages.changed)
      or (arch_mysql_install_packages is defined and arch_mysql_install_packages.changed) }}"

# Configure MySQL.
- include_tasks: configure.yml
- include_tasks: secure-installation.yml
- include_tasks: databases.yml
- include_tasks: users.yml
- include_tasks: replication.yml
- include_tasks: mysqld_exporter.yml
  tags: mysqld_exporter
- include_tasks: prometheus.yml
