---
- name: Transfer node_info from Monitornode to PromethusServer
  shell: sshpass -p "ops@ops" ssh -o stricthostkeychecking=no root@************* """echo "{{ansible_default_ipv4['address']}}" > /opt/tools/mysql_list"""
  ignore_errors: true
  tags:
    - prometheus

- name: Join Prometheus Monitoring
  shell: sshpass -p "ops@ops" ssh -o stricthostkeychecking=no root@************* """/usr/bin/python /opt/tools/join_mysql.py"""
  ignore_errors: true
  tags:
    - prometheus
