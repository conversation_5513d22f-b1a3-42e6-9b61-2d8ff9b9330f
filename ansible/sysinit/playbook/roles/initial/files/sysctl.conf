# sysctl settings are defined through files in
# /usr/lib/sysctl.d/, /run/sysctl.d/, and /etc/sysctl.d/.
#
# Vendors settings live in /usr/lib/sysctl.d/.
# To override a whole file, create a new file with the same in
# /etc/sysctl.d/ and put new settings there. To override
# only specific settings, add a file with a lexically later
# name in /etc/sysctl.d/ and put new settings there.
#
# For more information, see sysctl.conf(5) and sysctl.d(5).
vm.dirty_ratio = 40
vm.dirty_background_ratio = 20
vm.dirty_writeback_centisecs = 2
vm.dirty_expire_centisecs = 3
vm.drop_caches = 2
vm.vfs_cache_pressure = 100
vm.lowmem_reserve_ratio = 32 32 8
net.ipv4.tcp_max_syn_backlog = 8192
net.ipv4.tcp_syncookies = 1
net.ipv4.tcp_mem = 178368 524288 8048576
net.ipv4.tcp_wmem = 4096 16384 4194304
net.ipv4.tcp_rmem = 4096 87380 4194304
net.core.netdev_max_backlog = 1000
net.ipv4.ip_local_port_range = 1024 65000
net.core.somaxconn = 1000
net.ipv4.tcp_tw_reuse = 1
