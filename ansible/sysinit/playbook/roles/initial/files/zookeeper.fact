#!/usr/bin/python

import json
import subprocess

def main():
    data = {}
    service_name = "zookeeper"
    try:
	    pid = subprocess.Popen("""ps -aux | grep %s |grep zoo.cfg| grep -vE "color|kafka_exporter|python|aux|grep"|awk '{print $2}'"""%service_name,
	                         shell=True,
	                         stdout=subprocess.PIPE,
	                         stderr=subprocess.PIPE)
            pid = pid.stdout.read().replace('\n','')
            service = subprocess.Popen("systemctl status %s | grep Active | awk '{print $2}'"%service_name,
                                 shell=True,
                                 stdout=subprocess.PIPE,
                                 stderr=subprocess.PIPE)
            service = service.stdout.read().replace('\n','')
            service_pid = subprocess.Popen("systemctl status %s | grep PID | awk '{print $3}'"% service_name,
                                 shell=True,
                                 stdout=subprocess.PIPE,
                                 stderr=subprocess.PIPE)
            service_pid=service_pid.stdout.read().replace('\n','')
            #port =  subprocess.Popen("netstat -nlp | grep %s |awk '{print $4}' | awk -F ':::' 'NR==3 {print $2}'"%pid,
            port =  subprocess.Popen("netstat -nlp | grep %s |awk '{split($4,a,\":\");if(a[2]!=\"\"){print a[2];}else{print a[4];}}'"%pid,
                                 shell=True,
                                 stdout=subprocess.PIPE,
                                 stderr=subprocess.PIPE)
            port = port.stdout.read().replace('\n','')
            config_path = subprocess.Popen("systemctl status %s| grep config | awk -F ' ' '{print $8}'"% service_name,
                                 shell=True,
                                 stdout=subprocess.PIPE,
                                 stderr=subprocess.PIPE)
            config_path = config_path.stdout.read().replace('\n','')
            config_sp=config_path.split('..',1)
            path1 = config_sp[0][:-5]
            config_fp = path1 + config_sp[1]
            config_context = subprocess.Popen("cat %s | grep clientPort"% config_fp,
                                 shell=True,
                                 stdout=subprocess.PIPE,
                                 stderr=subprocess.PIPE)
            config_context = config_context.stdout.read().replace('\n','')
            config_port = config_context.split('=')

            
            if service == 'active':
              if service_pid == pid: 
                data["port"]= config_port[1]
	        
	    
	    	
	    print json.dumps(data)
    
    except Exception as e:
        print json.dumps(data)
    	
  
if __name__=='__main__':
    main()
