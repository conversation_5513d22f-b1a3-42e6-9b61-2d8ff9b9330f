---
- name: Transfer node_info from Monitornode to PromethusServer
  shell: sshpass -p "Centos@ops2020" ssh -o stricthostkeychecking=no root@************* """echo "{{ansible_default_ipv4['address']}}" > /opt/tools/node_list"""
  ignore_errors: true
  tags:
    - promethus

- name: Join Prometheus Monitoring
  shell: sshpass -p "Centos@ops2020" ssh -o stricthostkeychecking=no root@************* """/usr/bin/python /opt/tools/join_node.py"""
  ignore_errors: true
  tags:
    - promethus
