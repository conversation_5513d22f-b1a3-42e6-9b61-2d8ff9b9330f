---
- name: copy docker-ce.repo
  copy:
    src: "{{ item }}"
    dest: /etc/yum.repos.d/{{ item }}
  with_items:
    - docker-ce.repo
  tags: docker

- name: install docker-ce
  yum:
    name:
      - docker-ce
    update_cache: true
    state: present
  tags: docker

- name: enable docker
  service:
    name: docker
    state: started
    enabled: true
  tags:
    - docker

- name: install docker-py
  yum:
    name: python-docker-py
    state: present
    update_cache: true
  tags:
    - docker
    - docker-py

- name: locale docker mirror
  copy:
    src: docker-mirror.json
    dest: /etc/docker/daemon.json
  tags:
    - docker
    - docker_registry
  notify: restart docker

- name: login docker mirror
  docker_login:
    username: admin
    password: c@e@Hs71nXuc8Aek
    registry: harbor.ops.cn
  tags:
    - docker
    - docker_login
