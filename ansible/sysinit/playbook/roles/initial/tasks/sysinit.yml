---
- name: resolv.conf setup
  copy:
    src: resolv.conf
    dest: /etc/resolv.conf
    mode: "0644"
    backup: true
  tags:
    - sysinit
    - dns_setup

#- name: dns setup
#  lineinfile:
#    dest: "/etc/resolv.conf"
#    regexp: "^#?nameserver {{ item }}$"
#    line: "nameserver {{ item }}"
#    state: present
#  with_items:
#    - ***********
#    - ***********
#  tags:
#    - sysinit
#    - dns_setup

- name: Create Dir
  file: path={{ item }} state=directory
  with_items:
    - /opt/tools
    - /opt/scripts
    - /opt/database
    - /opt/backup
    - /root/.pip
  tags:
    - sysinit

- name: Create Group ops
  group:
    name: ops
    state: present
  when: "'ldap' not in host_type"
  tags:
    - sysinit
    - create_user

- name: Create User ops
  user:
    name: ops
    shell: /bin/bash
    group: ops
  when: "'ldap' not in host_type"
  tags:
    - sysinit
    - create-user

- name: Create Ops User Dir
  file: path={{ item }} state=directory mode='0755' owner=ops group=root
  with_items:
    - /opt/ops/app
    - /opt/ops/files
  when: "'ldap' not in host_type"
  tags:
    - sysinit
    - changedirper

- name: Create Log Dir
  file:
    path: /var/log/app
    state: directory
    mode: "0755"
  tags:
    - sysinit

- name: back yum repo file
  shell: mv /etc/yum.repos.d /etc/yum.repos.d-$(date  +%F-%H-%M-%S)
  tags:
    - create_repo
    - sysinit

- name: Create yum repo Dir
  file:
    path: /etc/yum.repos.d
    state: directory
    mode: "0755"
  tags:
    - sysinit
    - create_repo
    - create_repo_dir

- name: Add repo ops
  yum_repository:
    name: ops
    description: ops YUM repo
    file: ops
    baseurl: http://*************/7/$basearch/
    enabled: true
    gpgcheck: false
  tags:
    - sysinit
    - repo_ops
    - create_repo

- name: Add RedHat System Parameter File
  copy: src={{ item.src }} dest={{ item.dest }} mode='0755' backup=yes
  with_items:
    - { src: redhat-base.repo, dest: /etc/yum.repos.d/redhat-base.repo }
    - { src: epel.repo, dest: /etc/yum.repos.d/epel.repo }
    - { src: epel-testing.repo, dest: /etc/yum.repos.d/epel-testing.repo }
    - { src: limits.conf, dest: /etc/security/limits.conf }
    - { src: sysctl.conf, dest: /etc/sysctl.conf }
    - { src: docker-ce.repo, dest: /etc/yum.repos.d/docker-ce.repo }
    - { src: gpfs.repo, dest: /etc/yum.repos.d/gpfs.repo }
    - { src: pip.conf, dest: /root/.pip/pip.conf }
  when: ansible_distribution == 'RedHat'
  tags:
    - base_repo
    - config
    - create_repo

- name: Add CentOS System Parameter File
  copy: src={{ item.src }} dest={{ item.dest }} mode='0755' backup=yes
  with_items:
    - { src: CentOS-Base.repo, dest: /etc/yum.repos.d/CentOS-Base.repo }
    - { src: epel.repo, dest: /etc/yum.repos.d/epel.repo }
    - { src: epel-testing.repo, dest: /etc/yum.repos.d/epel-testing.repo }
    - { src: limits.conf, dest: /etc/security/limits.conf }
    - { src: sysctl.conf, dest: /etc/sysctl.conf }
    - { src: docker-ce.repo, dest: /etc/yum.repos.d/docker-ce.repo }
    - { src: gpfs.repo, dest: /etc/yum.repos.d/gpfs.repo }
    - { src: pip.conf, dest: /root/.pip/pip.conf }
  when: ansible_distribution == "CentOS"
  tags:
    - base_repo
    - config
    - create_repo

- name: Install Basic Lib
  yum: name={{ item }} state=latest update_cache=yes
  with_items:
    - libselinux-python
    - glibc
    - gcc
    - make
    - cmake
    - zlib
    - kexec-tools
  tags:
    - sysinit
    - install_basic_lib

- name: Install Basic Tools
  yum: name={{ item }} state=latest update_cache=yes
  with_items:
    - vim
    - wget
    - lrzsz
    - unzip
    - zip
    - net-tools
    - curl
    - lsof
    - iotop
    - ntp
    - sysstat
    - python-pip
    - sshpass
  tags:
    - sysinit
    - install_basic_tools

- name: Change system.conf DefaultLimitCORE
  lineinfile:
    dest: /etc/systemd/system.conf
    regexp: DefaultLimitCORE
    backrefs: false
    line: DefaultLimitCORE=infinity
  tags:
    - sysinit

- name: Change system.conf DefaultLimitNOFILE
  lineinfile:
    dest: /etc/systemd/system.conf
    regexp: DefaultLimitNOFILE
    backrefs: false
    line: DefaultLimitNOFILE=1048576
  tags:
    - sysinit

- name: Change system.conf
  lineinfile:
    dest: /etc/systemd/system.conf
    regexp: DefaultLimitNPROC
    backrefs: false
    line: DefaultLimitNPROC=100000
  tags:
    - sysinit
    - change_system_parameter

- name: Selinux Disable
  lineinfile:
    dest: /etc/selinux/config
    regexp: ^SELINUX=
    line: SELINUX=disabled
  tags:
    - sysinit
    - selinux_dsiable

- name: Selinux stop
  selinux: state=disabled
  tags:
    - sysinit
    - selinux_stop

- name: Set Timezone
  timezone: name=Asia/Shanghai
  tags:
    - sysinit
    - set_timezone

- name: ssh connect timeout setup
  lineinfile:
    dest: /etc/ssh/sshd_config
    regexp: ^#?{{ item.option }}.+$
    line: "{{ item.option }} {{ item.value }}"
    state: "{{ item.state | default('present') }}"
  with_items: "{{ ssh_config }}"
  notify: restart sshd
  tags:
    - sysinit
    - ssh_timeout

- name: history record env set
  lineinfile:
    dest: /etc/bashrc
    regexp: ^#?{{ item.option }}.+$
    line: "{{ item.option }}={{ item.value }}"
    state: "{{ item.state | default('present') }}"
  with_items: "{{ etc_bashrc }}"
  tags:
    - sysinit
    - history_record

- name: command record
  lineinfile:
    dest: /etc/rsyslog.conf
    regexp: ^#?local5.+$
    line: local5.*  /var/log/history.log
    state: present
  notify: restart rsyslog
  tags:
    - history_record

- name: ntp date sync
  lineinfile:
    dest: /etc/ntp.conf
    regexp: ^#?server {{ item.index }}.+$
    line: server {{ item.ip }} iburst
    state: present
  with_items:
    - { index: "0", ip: *********** }
    - { index: "1", ip: *********** }
  tags:
    - ntp

- name: Disabled chronyd
  service:
    name: chronyd
    state: stopped
    enabled: false
  tags:
    - ntp

- name: Enabled ntpd
  service:
    name: ntpd
    state: started
    enabled: true
  tags:
    - ntp

- name: Enabled kdump
  service:
    name: kdump
    state: started
    enabled: true
  tags:
    - kdump

- name: 虚拟机ntpd 配置文件修改
  lineinfile:
    dest: /etc/sysconfig/ntpd
    regexp: ^#?OPTIONS=.+$
    line: OPTIONS="-g -L"
    state: present
  notify: restart ntpd
  tags:
    - ntp
    - ntp_cfg

#- name: update root password
#  user:
#    name: root
#    password: "{{ root_password }}"
#  tags:
#    - passwd

- name: Create custom fact directory
  file:
    path: /etc/ansible/facts.d
    state: directory
  tags:
    - fact

- name: Insert custom fact file
  copy:
    src: "{{ item }}.fact"
    dest: /etc/ansible/facts.d/{{ item }}.fact
    mode: "0755"
  with_items:
    - "{{ ansible_fact }}"
  tags:
    - fact
