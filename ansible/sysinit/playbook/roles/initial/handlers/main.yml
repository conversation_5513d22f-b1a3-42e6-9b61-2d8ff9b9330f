---
- name: restart zabbix-agent
  service: name=zabbix-agent state=restarted
  tags:
    - restart_zabbix

- name: restart sshd
  service: name=sshd state=restarted
  tags:
    - restart_sshd

- name: restart docker
  service:
    name: docker
    state: restarted
  tags:
    - restart_docker

- name: restart rsyslog
  service:
    name: rsyslog
    state: restarted
  tags:
    - restart_rsyslog

- name: restart node-exporter
  service:
    name: node-exporter
    enabled: true
    state: restarted
  tags:
    - node_exporter

- name: restart ntpd
  service:
    name: ntpd
    enabled: true
    state: restarted
  tags:
    - restart_ntpd
