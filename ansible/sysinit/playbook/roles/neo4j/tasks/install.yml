---
- name: wget neo4j.tar.gz
  get_url: dest=/tmp/neo4j-community-3.5.8-unix.tar.gz force=yes url=http://172.16.56.39/neo4j/neo4j-community-3.5.8-unix.tar.gz
  tags:
    - neo4j

- name: unarchive neo4j.tar.gz
  unarchive: src=/tmp/neo4j-community-3.5.8-unix.tar.gz dest=/opt/ops/app group=neo4j owner=neo4j mode=0755 copy=no
  tags:
    - neo4j

- name: config systemctl
  copy:
    src: neo4j.service
    dest: /usr/lib/systemd/system/
  tags:
    - neo4j

- name: config Neo4j.conf
  template:
    src: neo4j.conf.j2
    dest: /opt/ops/app/neo4j-community-3.5.8/conf/neo4j.conf
  notify: restart neo4j
  tags:
    - neo4j

- name: systemctl enable neo4j
  shell: systemctl enable neo4j && systemctl start neo4j
