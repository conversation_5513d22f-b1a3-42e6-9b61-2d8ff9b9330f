---
language: python
services: docker

env:
  global:
    - ROLE_NAME: mysql
  matrix:
    - MOLECULE_DISTRO: centos7
      MOLECULE_DOCKER_COMMAND: /usr/lib/systemd/systemd
    - MOLECULE_DISTRO: centos6
    - MOLECULE_DISTRO: ubuntu1604
    - M<PERSON>ECULE_DISTRO: debian8

install:
  # Install test dependencies.
  - pip install molecule docker

before_script:
  # Use actual Ansible Galaxy role name for the project directory.
  - cd ../
  - mv ansible-role-$ROLE_NAME geerlingguy.$ROLE_NAME
  - cd geerlingguy.$ROLE_NAME

script:
  # Run tests.
  - molecule test

notifications:
  webhooks: https://galaxy.ansible.com/api/v1/notifications/
