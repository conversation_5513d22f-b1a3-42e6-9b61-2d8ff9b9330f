---
- hosts:
    - ************
    - ************
    - ************
    - ************
  #- hosts: ************
  #- hosts: "{{ host_name }}"
  become: true
  pre_tasks:
    - name: Install the MySQL repo.
      copy:
        dest: /etc/yum.repos.d/mysql-community.repo
        src: roles/mysql/files/mysql-community.repo
        mode: "0755"
        backup: true
      when: ansible_os_family == "RedHat"

    - name: mkdir /opt/database dir
      file:
        path: /opt/database
        state: directory

    - name: Override variables for MySQL (RedHat).
      set_fact:
        mysql_daemon: mysqld
        mysql_packages: [mysql-community-server]
        mysql_log_error: /var/log/mysqld.err
        mysql_syslog_tag: mysqld
        mysql_pid_file: /var/run/mysqld/mysqld.pid
        mysql_socket: /var/lib/mysql/mysql.sock
      when: ansible_os_family == "RedHat"
  roles:
    - { role: mysql }
