version: '3'

tasks:
  ping:
    cmds:
      - ansible {{.CLI_ARGS}} --list-host # 查看组中的host清单
      - ansible {{.CLI_ARGS}} -m ping -vvv # 确保ssh能够连接所有目标服务器(请一定确认ssh连接已建立，因为未建立ssh连接也会成功)
      - ansible {{.CLI_ARGS}} -a uptime
      - ansible {{.CLI_ARGS}} -m command -a 'echo Hello World on Docker.'
  #      - ansible rancher -a 'hostname' -vvv
  #      - ansible rancher --list
  #
  #      - ansible rancher -a 'docker ps -a'
  #
  #      - ansible rancher --check -m script -a './k8s-install.sh' -i hosts
  #      - ansible rancher -m script -a './k8s-install.sh' -i hosts
  #      - ansible rancher -m script -a './k8s-install.sh'


  centos:
    cmds:
      - ansible-playbook -v playbook/centos.yml -i hosts # 执行并查看输出细节

#      - # ansible-playbook -v playbook/helloworld.yml -i hosts
#      - # ansible-playbook -v playbook/k8s.yml -i hosts
#      - # ansible-playbook -v playbook/hk.yml -i hosts
#      - ansible-playbook playbook/centos.yml -i hosts -l ip
#      - ansible-playbook playbook/vps.yml -i hosts -l ip
#
#      - ansible-galaxy install gantsign.oh-my-zsh # 需要先fetch remote roles
#
#      # [Ansible Galaxy - samdoran.caddy](https://galaxy.ansible.com/ui/standalone/roles/samdoran/caddy/)
#      - ansible-galaxy role install samdoran.caddy
#
#      # [gantsign/ansible-role-oh-my-zsh: Ansible role for installing and configuring oh-my-zsh](https://github.com/gantsign/ansible-role-oh-my-zsh)

#      # [Ansible Galaxy - fesaille.gh](https://galaxy.ansible.com/ui/standalone/roles/fesaille/gh/)
#      - ansible-galaxy role install fesaille.gh
#
#      - ansible asb -m copy -a "src=/home/<USER>/playbook/pb_shell.sh dest=/home/<USER>/playbook/" # 把文件cp到目标机器
#    preconditions:
#      - ansible-playbook -v playbook/centos.yml --list-hosts # 查看脚本影响到的hosts
#      - ansible-playbook --check playbook/centos.yml -i hosts # 预执行，查看playbook语法是否正确，以及在目标服务器上是否能够执行成功;(但是并不保证一定能够执行成功)



