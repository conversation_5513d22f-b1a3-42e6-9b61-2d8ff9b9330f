#--1.hosts
cat >> /etc/hosts << EOF
************ k8s-master
************ k8s-node1
************ k8s-node2
EOF

# 部署k8s时，为什么要关闭SELinux/防火墙/内存swap?
# 防火墙: nftables后端兼容性问题，产生重复的防火墙规则;
# SELinux: 关闭SELinux以允许容器访问宿主机的文件系统;
# swap: 性能问题，可能会出问题;


#--2.firewall
systemctl stop firewalld && systemctl disable firewalld
systemctl disable firewalld.service

# SELinux
setenforce 0
sed -i 's/SELINUX=enforcing/SELINUX=disabled/g' /etc/selinux/config
# 关闭swap
swapoff -a

#--3.iptables
cat <<EOF | sudo tee /etc/modules-load.d/k8s.conf
br_netfilter
EOF

cat <<EOF | sudo tee /etc/sysctl.d/k8s.conf
net.bridge.bridge-nf-call-ip6tables = 1
net.bridge.bridge-nf-call-iptables = 1
EOF
sudo sysctl --system

#--4.swap
swapoff -a
# systemctl disable swap

#--5.docker
cat <<EOF | sudo tee /etc/docker/daemon.json
{
  "registry-mirrors": ["https://w60mq0zz.mirror.aliyuncs.com"],
  "exec-opts": ["native.cgroupdriver=systemd"],
  "log-driver": "json-file",
  "log-opts": {
    "max-size": "100m"
  },
  "storage-driver": "overlay2"
}
EOF

#--6.aliyun
# 适用于centos7
# 根据需要修改gpgcheck/repo_gpgcheck等参数
cat <<EOF > /etc/yum.repos.d/kubernetes.repo
[kubernetes]
name=Kubernetes
baseurl=https://mirrors.aliyun.com/kubernetes/yum/repos/kubernetes-el7-x86_64/
enabled=1
gpgcheck=1
repo_gpgcheck=1
gpgkey=https://mirrors.aliyun.com/kubernetes/yum/doc/yum-key.gpg
      https://mirrors.aliyun.com/kubernetes/yum/doc/rpm-package-key.gpg
EOF