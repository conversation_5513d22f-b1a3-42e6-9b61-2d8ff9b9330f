---

# [2025-04-01] yum源已经not found了
#- name: 添加yum源
#  yum_repository:
#    name: epel_repo
#    description: EPEL YUM repo
#    baseurl: http://dl.fedoraproject.org/pub/epel/7/x86_64/

# [Fedora, CentOS, Red Hat Enterprise Linux (dnf)](https://github.com/cli/cli/blob/trunk/docs/install_linux.md#fedora-centos-red-hat-enterprise-linux-dnf)
# [ansible.builtin.dnf – Manages packages with the dnf package manager — Ansible Documentation](https://docs.ansible.com/ansible/latest/collections/ansible/builtin/dnf_module.html)

# The Python 2 yum module is needed for this module. If you require Python 3 support use the `dnf` Ansible module instead.
#- name: install dnf-command
#  dnf:
#    name: 'dnf-command(config-manager)'

- name: 使用dnf安装基础库
  dnf: name={{ item }} state=latest update_cache=yes
  with_items:
    - epel-release
    - libselinux-python
    - glibc
    - gcc
    - make
    - cmake
    - zlib
#    - yum-utils
    - gcc-c++
    - zip
    - net-tools
    - lrzsz
    - wget
    - curl
    - telnet
    - iotop
    - vim
    - dmidecode
    - sysstat
    - ntp
    - net-snmp
    - rsync
    - git
    - dnf
    - dnf-plugins-core
    - lsof
    - screen
    - tree
    - dos2unix
    - tcpdump
    - setuptool
    - psmisc
    - openssl
    - openssl-devel
    - bind-utils
    - traceroute
    - bash-completion

#- name: yum upgrade
#  shell: yum -y upgrade

#- name: install gh
#  dnf:
#    enablerepo: https://cli.github.com/packages/rpm/gh-cli.repo
#    name: 'gh'
#    state: present

- name: install gh
  shell: |
    dnf config-manager --add-repo https://cli.github.com/packages/rpm/gh-cli.repo
    dnf install -y gh
    dnf update gh
