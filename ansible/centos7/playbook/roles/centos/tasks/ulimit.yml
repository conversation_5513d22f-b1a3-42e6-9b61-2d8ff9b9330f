---
########Ulimit Init########
- name: Ulimit change
  shell: ulimit -SHn 102400

- name: Ulimit change rc.local
  lineinfile:
    dest: /etc/rc.local
    regexp: ulimit -SHn 102400
    backrefs: false
    line: ulimit -SHn 102400

- name: Change limits.conf soft
  lineinfile:
    dest: /etc/security/limits.conf
    regexp: \* soft nofile [0-9]+
    backrefs: false
    line: "* soft nofile 102400"

- name: Change limits.conf hard
  lineinfile:
    dest: /etc/security/limits.conf
    regexp: \* hard nofile [0-9]+
    backrefs: false
    line: "* hard nofile 102400"

- name: Change system.conf DefaultLimitCORE
  lineinfile:
    dest: /etc/systemd/system.conf
    regexp: DefaultLimitCORE
    backrefs: false
    line: DefaultLimitCORE=infinity

- name: Change system.conf DefaultLimitNOFILE
  lineinfile:
    dest: /etc/systemd/system.conf
    regexp: DefaultLimitNOFILE
    backrefs: false
    line: DefaultLimitNOFILE=100000

- name: Change system.conf
  lineinfile:
    dest: /etc/systemd/system.conf
    regexp: DefaultLimitNPROC
    backrefs: false
    line: DefaultLimitNPROC=100000
