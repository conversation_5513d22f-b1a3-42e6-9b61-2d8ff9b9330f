---
# [Alias Rm to Mv](https://cloud.tencent.com/developer/article/1562020)
- name: alias rm to mv
  shell: |
    mkdir ~/.trash
    echo -e "alias rm='move1(){ /bin/mv -f \$@ ~/.trash/; };move1 \$@'" >> ~/.zshrc
    source ~/.zshrc
# [shell命令--一行命令写入crontab自动任务_打卤的博客-CSDN博客_shell 写入crontab](https://blog.csdn.net/liyyzz33/article/details/104893537)
# 覆盖,避免重复
# [使用shell脚本或命令行 添加crontab 定时任务_Gafx的博客-CSDN博客_用shell脚本设定crontab命令](https://blog.csdn.net/mzc11/article/details/81842534)
- name: 写入cron任务
  shell: echo '111'


#- name: root用户密码输入错误三次，锁定账户一段时间
#  shell: |
#    sed -i "1a\auth required pam_tally2.so deny=3 unlock_time=300 even_deny_root root_unlock_time=10" /etc/pam.d/sshd # 限制用户远程登录
#    sed -i "1a\auth required pam_tally2.so deny=3 lock_time=300 even_deny_root root_unlock_time=10" /etc/pam.d/login # 限制用户从tty登录

- name: history命令显示操作时间
  shell: export HISTTIMEFORMAT="[%Y.%m.%d %H:%M:%S-$USER_IP-$USER]"
- name: set ~/.vimrc
  shell: echo '.vimrc'
