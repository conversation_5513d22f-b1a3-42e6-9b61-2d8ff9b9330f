---
########Ssh Init#######
- name: Open ssh PubkeyAuthentication
  lineinfile:
    dest: /etc/ssh/sshd_config
    regexp: "#PubkeyAuthentication yes"
    backrefs: true
    line: PubkeyAuthentication yes

- name: Open ssh AuthorizedKeysFile
  lineinfile:
    dest: /etc/ssh/sshd_config
    regexp: "#AuthorizedKeysFile"
    backrefs: true
    line: AuthorizedKeysFile

- name: 关闭ssh密码登录
  lineinfile:
    dest: /etc/ssh/sshd_config
    regexp: ^PasswordAuthentication yes
    backrefs: true
    line: PasswordAuthentication no

- name: 修改ssh端口
  lineinfile:
    dest: /etc/ssh/sshd_config
    regexp: "#Port 22"
    backrefs: true
    line: Port 8022

- name: Echo /etc/ssh/sshd_config
  shell: egrep "Port|AuthorizedKeysFile|PubkeyAuthentication|PasswordAuthentication" /etc/ssh/sshd_config

- name: Create .ssh
  file: path=/root/.ssh owner=root group=root mode=700 state=directory

#- name: Add keys
#  copy: src=public_key/authorized_keys dest=/root/.ssh/authorized_keys owner=root group=root mode=600

- name: Restart sshd
  service: name=sshd state=restarted enabled=yes

#- name: Check sshd status
