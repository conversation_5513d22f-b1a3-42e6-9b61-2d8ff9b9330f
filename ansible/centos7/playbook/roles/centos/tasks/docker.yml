---
- name: add docker repo
  shell: yum-config-manager --add-repo https://download.docker.com/linux/centos/docker-ce.repo
#- name: install docer-ce
#  yum:
#    name: docker-ce
#    state: present
#- name: install docker-ce-cli
#  yum:
#    name: docker-ce-cli
#    state: present
#- name: install containerd.io
#  yum:
#    name: containerd.io
#    state: present

- name: 安装docker (相当于上面三步)
  shell: curl -fsSL https://get.docker.com/ | sh

- name: 创建docker.json文件夹
  ignore_errors: true
  shell: mkdir /etc/docker

#- name: 修改docker镜像源 (一定要修改mirror，否则国内主机无法pull image)
#  copy: src=./files/docker-daemon.json dest=/etc/docker/daemon.json
#  tags: configmirro


- name: 添加当前用户到docker用户组
  shell: sudo usermod -a -G docker $USER
- name: start enable docker
  service: name=docker state=started enabled=true
- name: 开启服务自启，并启动服务(如果systemctl无法启动，则使用service)
  shell: |
    sudo systemctl daemon-reload
    sudo systemctl restart docker
    systemctl start docker
    systemctl enable docker
  tags: restart
#- service: name=docker enabled=yes state=started
- name: 查看docker服务是否启动
  shell: docker --version
- name: 安装docker及docker-compose
  shell: |
    yum install -y docker-compose
    docker-compose --version
