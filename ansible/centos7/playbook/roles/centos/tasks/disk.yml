---
########Disk Init########
#- name: New Disk Partition
#  script: shell/disk.sh "{{ disk }}" #执行 disk.sh 参数{{ disk }} 对应xfs.yml的disk:  /dev/vdb #磁盘名字
#  become: yes
#  become_method: sudo

#- name: New Disk Format(xfs)
#  filesystem: fstype=xfs dev="{{ partition }}" opts="-fn ftype=1" #格式化磁盘分区
#  become: yes
#  become_method: sudo

#- name: New Disk Mount
#  mount: name="{{ mountDir }}" src="{{ partition }}" fstype=xfs state=mounted #挂在目录
#  become: yes
#  become_method: sudo

########Create Directory########
- name: Create Directory
  file: path={{ item }} state=directory
  with_items:
    - /opt/hxapps
    - /opt/hxwww
    - /opt/hxlog/
    - /opt/hxscripts
    - /opt/hxupload
    - /opt/hxbackup
