---
- name: say 'hello world'
  hosts: all
  tasks:
    - name: echo 'hello world'
      command: echo 'hello world'
      register: result
    - name: print stdout
      debug:
        msg: hello, world
- hosts: hk
  remote_user: root
  roles:
    - { role: centos }

#- name: install omz
#  hosts: hk
#  tags: omz
#  roles:
#    - role: gantsign.oh-my-zsh
#      users:
#        - username: root
#      shell: |
#        chsh -s /bin/zsh root
#        echo $SHELL

