---
#- include: group.yml
#- include: user.yml
#- name: install package
# yum: name=nginx
#- name: copy file
# copy: src=index.html dest={{ web_root }} owner=nginx
#- name: copy conf
#template: src=nginx.conf.j2 dest=/etc/nginx/nginx.conf
#  notify: restart nginx
#  - name: start nginx
#    service: name=nginx state=started enabled=true

- import_tasks: group.yml
- import_tasks: user.yml
- import_tasks: yum.yml
- import_tasks: templ.yml
- import_tasks: stservice.yml
# 如果需要调用别的角色的yaml文件，也可以这样写，比如：- include：roles/httpd/tasks/copyfile.yaml
# 注意：copyfiile.yaml文件中的源路径必须是绝对路径
