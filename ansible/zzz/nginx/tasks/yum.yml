---
# enable EPEL repo by installing the epel-release package
# [amazon ec2 - Ansible "No package matching 'nginx' found available, installed or updated" - Stack Overflow](https://stackoverflow.com/questions/70117790/ansible-no-package-matching-nginx-found-available-installed-or-updated)
- name: install EPEL repo
  become: true
  yum: name=epel-release state=present
- name: install package
  yum: name=nginx
