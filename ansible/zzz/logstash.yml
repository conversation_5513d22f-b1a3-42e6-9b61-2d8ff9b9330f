# 1. 更新logstash.yml 文件并推送到集群中
# 2. 重启logstash服务
# YAML 还有一个怪癖. 所有的 YAML 文件(无论和 Ansible 有没有关系)开始行都应该是 ---. 这是 YAML 格式的一部分, 表明一个文件的开始.
# Ansible 使用 “{{ var }}” 来引用变量，foo: "{{ variable }}"
---
- name: deploy logstash
  remote_user: root
  hosts: test_logstash
  tasks:
    #拷贝配置文件到远程服务器
    - name: 推送配置文件
      copy:
        src: logstash.yml
        dest: /root/logstash/logstash-6.4.3/config
        owner: root
        backup: true
    #执行重启命令
    - name: 重启logstash
      systemd:
        name: logstash
        state: restarted
