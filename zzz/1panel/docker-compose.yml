version: '3'
services:
  1panel:
    container_name: 1panel # 容器名
    restart: unless-stopped
    network_mode: "host"
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
      - /var/lib/docker/volumes:/var/lib/docker/volumes
      - /opt:/opt  # 文件存储映射
      - /root:/root  # 可选的文件存储映射
    environment:
      - TZ=Asia/Shanghai  # 时区设置
    image: dp.hxha.xyz/moelin/1panel:latest
    labels:
      createdBy: "Apps"
 