{"log": {"access": "/var/log/v2ray/access.log", "error": "/var/log/v2ray/error.log", "loglevel": "warning"}, "inbounds": [{"listen": "0.0.0.0", "port": 1310, "protocol": "vmess", "settings": {"clients": [{"id": "ac0cd527-3e4c-4c93-b22e-45a65472ffa7", "alterId": 0, "security": "auto"}]}, "streamSettings": {"network": "ws", "wsSettings": {"path": "/ws"}}, "mux": {"enabled": true}}], "outbounds": [{"protocol": "freedom", "tag": "freedom"}], "dns": {"servers": ["*******", "*******", "localhost"]}}