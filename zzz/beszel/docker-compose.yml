---

services:
  beszel:
    image: henrygd/beszel:latest
    container_name: beszel
    restart: unless-stopped
    extra_hosts:
      - host.docker.internal:host-gateway
    ports:
      - 8090:8090
    volumes:
      - ./beszel_data:/beszel_data

  beszel-agent:
    image: henrygd/beszel-agent:latest
    container_name: beszel-agent
    restart: unless-stopped
    network_mode: host
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:ro
    environment:
      PORT: 45876
      # 请勿删除密钥周围的引号
      KEY: 'sh-ed25519 AAAAC3NzaC1lZDI1NTE5AAAAIN8+nfnnfyDtFdP4BHGufmG/B2KOw3Pm6osx9ruU4yHU'

