version: '3'

services:
  rsshub:
    image: diygod/rsshub:latest
    container_name: rsshub
    restart: always
    ports:
      - '1200:1200'
    environment:
      NODE_ENV: production
      CACHE_TYPE: memory
#      PROXY_URI: 'socks5h://warp:1080'
#      PROXY_URI: 'http://192.168.64.2:1080'
#      PROXY_URL_REGEX: (..)?(bilibili)(/.)?
#      REDIS_URL: 'redis://redis:6379/'
      PUPPETEER_WS_ENDPOINT: 'ws://browserless:3000'
      YOUTUBE_KEY: 'AIzaSyBOtBvS5fXrFBaNQYp7cu9I2KoseOwXyOc'
      YUQUE_TOKEN: 'vmBtcJcQozr2DYYoYQirz1aKYowC9qWXXb4LVPrc'
      GITHUB_ACCESS_TOKEN: '****************************************'
      PIXIV_REFRESHTOKEN: 'U5BSKJoddgcwvV9RKqgFzpL293FzrYf7mHJTxWfBP7c'
      BILIBILI_COOKIE_3461573033068685: '580cd2d0%2C1744792044%2C99978%2Aa2CjAkMLyE0GEGOmylD1pZ-5o0zoHifRLCyGHp9-3HEzhyKk5UR0I22Oa2eyG5cG2cEbESVnFVbDQ1cWU5NkZvSkJLN0xZMmNUbklvSncyVTgwc0ZhcW5zcHF2SWwyTmdzOUtiQ2J1MnRXdGtyRDRGZU96MFBCOHJXS3YyQTVwUG42LWVsV0N3YnJBIIEC'
      SPOTIFY_CLIENT_ID: '2d9c779f80b9408eafee5e11c773569b'
      SPOTIFY_CLIENT_SECRET: '42c52d7bcd3d4a66a9522d6430367cc3'    



    depends_on:
      # - redis
      - browserless
      # - warp

  browserless:
    # See issue 6680
    image: browserless/chrome:1.43-chrome-stable
    container_name: rsshub-browserless
    restart: always
    ulimits:
      core:
        hard: 0
        soft: 0

#  warp:
#    image: aleskxyz/warp-svc:latest
#    container_name: rsshub-warp
#    ports:
#      - '1080:1080'
#    expose:
#      - 1080
#    restart: always
#    environment:
#      WARP_LICENSE: 2jn61O3X-54r08TpQ-9M8ZV7x4
#      FAMILIES_MODE: full
#    volumes:
#      - ./warp:/var/lib/cloudflare-warp


#  redis:
#    image: redis:6.2
#    container_name: rsshub-redis
#    restart: always
#    volumes:
#      - redis:/data


#volumes:
#  redis:
