rsshub.lucc.dev {
	encode gzip
	tls <EMAIL>

	reverse_proxy /* http://127.0.0.1:1200
}

alist.lucc.dev {
	encode gzip
	tls <EMAIL>

	reverse_proxy /* http://127.0.0.1:5244
}


mon.lucc.dev {                                                                                                             
        encode gzip                                                                                                          
        tls <EMAIL>                                                                                                    
                                                                                                                             
        reverse_proxy /* http://127.0.0.1:8090                                                                               
} 

# ttrss.hxha.xyz {
# 	encode gzip
# 	tls <EMAIL>
#
# 	reverse_proxy /* http://127.0.0.1:8010
# }


#miniflux.wrss.top {
#        encode gzip
#        tls <EMAIL>
 
#        reverse_proxy /* http://127.0.0.1:8010
