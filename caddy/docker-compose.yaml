version: "3"
services:
  caddy:
    container_name: caddy
    image: caddy:alpine
    restart: unless-stopped
    cap_add:
      - NET_ADMIN
    ports:
      - "80:80"
      - "80:80/udp"
      - "443:443"
      - "443:443/udp"
      - "1200"
      - "8090"
    #      - "8886"
    #      - "8888"
    #      - "8080"
    #      - "5244"
    #      - "181"
    #      - "8090"
    #      - "8010"
    volumes:
      - $PWD/Caddyfile:/etc/caddy/Caddyfile
      - $PWD/site:/srv
      # - caddy_data:/data
      # - caddy_config:/config
      - $PWD/data:/data
      - $PWD/config:/config
    network_mode: "host"

#networks:
#  host:
#    name: host
#    external: true

#volumes:
#  caddy_data:
#    external: true
#  caddy_config:
